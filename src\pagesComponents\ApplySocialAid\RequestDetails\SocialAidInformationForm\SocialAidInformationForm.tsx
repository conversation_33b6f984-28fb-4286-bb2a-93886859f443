import { Grid, GridItem, Text } from "@chakra-ui/react";
import Form<PERSON>ield from "components/Form/FormField";
import { useFormContext } from "context/FormContext";
import { Form, Formik } from "formik";
import useRequiredFields from "hooks/useFormRequiredFields";
import { useTranslation } from "next-i18next";
import { useEffect, useRef, useState } from "react";
import * as functions from "./functions";
import {
	TerminatedAr,
	MilitaryServiceStatusAr,
	Terminated,
	MilitaryServiceStatus,
	UNDER_45_AGE,
	DIVORCED,
	BORN_UNKNOWN_PARENTS,
	ChildEligibilityforWomeninDifficultyAr,
	ChildEligibilityforWomeninDifficulty,
	SPOUSE_INCAPACITATED_FOREIGNER,
	ABANDONED,
	CHILD_IN_DIFFICULT_SITUATION,
	ORPHANS_ONLY,
	CHILD_OF_PRISONER,
	CATEGORY_VALUE_MAPPING,
} from "config";
import { useRouter } from "next/router";
import { formatEmiratesID } from "utils/formatters";

function RequestDetailsForm({
	onSubmit,
	handleChangeEvent,
	formKey,
	handleSetFormikState,
	initialData,
	readOnly = false,
	isMartialEmpty,
	isMaritalInit,
	userAge = 0,
}) {
	const formikRef = useRef<any>(null);
	const { t } = useTranslation(["forms", "common"]);
	const router = useRouter();
	const { lookups } = useFormContext();
	const checkMartial = initialData.hasMaritalStatus;
	let [validationSchema] = useState(functions.getValidationSchema(t, userAge));
	let requiredList = useRequiredFields(validationSchema);
	const [dropDownError, setDropDownError] = useState(false);
	const updateDropdownValues = () => {
		let originalInitialValues: any = { ...functions.getInitialValues };
		Object.keys(initialData).forEach((key) => {
			// Apply category mapping for existing applications
			let mappedValue = initialData[key];
			if (key === "Category" && CATEGORY_VALUE_MAPPING[initialData[key]]) {
				mappedValue = CATEGORY_VALUE_MAPPING[initialData[key]];
				console.log(`Category mapping applied: ${initialData[key]} -> ${mappedValue}`);
			}

			if (key in lookups) {
				let indexOfItem = lookups[key].findIndex((val) => val.value === mappedValue);
				if (indexOfItem >= 0) originalInitialValues[key] = lookups[key][indexOfItem];
			} else {
				originalInitialValues[key] = mappedValue
					? JSON.parse(JSON.stringify(mappedValue))
					: mappedValue;
			}
			if (key === "MilitaryServiceStatus") {
				let arrayValues = router.locale === "ar" ? MilitaryServiceStatusAr : MilitaryServiceStatus;
				let indexOfItem = arrayValues.findIndex((val) => val.value == mappedValue);
				if (indexOfItem >= 0) originalInitialValues[key] = arrayValues[indexOfItem];
			}
			if (key === "Terminated") {
				let arrayValues = router.locale === "ar" ? TerminatedAr : Terminated;
				let indexOfItem = arrayValues.findIndex((val) => val.value == initialData[key]);
				if (indexOfItem >= 0) originalInitialValues[key] = arrayValues[indexOfItem];
			}
			if (key === "ChildEligibilityforWomeninDifficulty") {
				originalInitialValues["ChildEligibilityforWomeninDifficulty"] = [];
				let arrayValues =
					router.locale === "ar"
						? ChildEligibilityforWomeninDifficultyAr
						: ChildEligibilityforWomeninDifficulty;
				arrayValues.map((item) => {
					if (initialData[key].includes(item.value)) {
						originalInitialValues[key].push(item);
					}
				});
			}
		});
		return originalInitialValues;
	};

	useEffect(() => {
		if (dropDownError) {
			setTimeout(() => {
				setDropDownError(false);
			}, 3000);
		}
	}, [dropDownError]);

	const [initialValues, setInitialValues] = useState(() => updateDropdownValues());

	const beneficiaryAge = userAge;

	useEffect(() => {
		setInitialValues(updateDropdownValues());
		// eslint-disable-next-line react-hooks/exhaustive-deps
	}, [lookups]);

	const getSubCategoryOptions = (reason) => {
		return lookups.SubCategory.filter((item) => item.RelatedId === reason.value);
	};
	return (
		<Formik
			enableReinitialize
			initialValues={initialValues}
			validationSchema={validationSchema}
			onSubmit={onSubmit}
			innerRef={formikRef}
			validateOnMount
		>
			{(formik: any) => {
				handleSetFormikState(
					{
						isLoading: formik.isSubmitting,
						isDisabled: !formik.isValid || formik.isSubmitting,
					},
					formKey
				);
				return (
					<Form
						onSubmit={(e) => {
							e.preventDefault();
							formik.handleSubmit(e);
						}}
						onChange={(e) => {
							e.preventDefault();
							functions.onChange(e, formik);
						}}
					>
						<Grid
							rowGap={{ base: 6, md: 6 }}
							columnGap={6}
							templateColumns="repeat(2, 1fr)"
							templateRows="auto"
						>
							{/* Main reason for social aid */}

							<GridItem colSpan={{ base: 2, md: 1 }}>
								<FormField
									type="selectableTags"
									value={formik.values["Category"]}
									isRequired={requiredList["Category"] || false}
									name="Category"
									options={lookups.Category}
									label={t("Category")}
									placeholder={t("placeholder", { ns: "common" })}
									error={formik.errors["Category"]}
									touched={formik.touched["Category"]}
									isDisabled={readOnly}
									onChange={(firstArg) => {
										handleChangeEvent("selectableTags", firstArg, "Category", formik, formKey);
										handleChangeEvent("selectableTags", "", "SubCategory", formik, formKey);
										// if its an orphan category, set sub category to empty, this is to avoid validation errors
									}}
								/>
							</GridItem>

							{/* Sub reason for social aid */}
							{
								<GridItem colSpan={{ base: 2, md: 1 }}>
									{formik.values["Category"] && (
										<>
											<FormField
												type="selectableTags"
												value={formik.values["SubCategory"]}
												isRequired={true}
												name="SubCategory"
												options={getSubCategoryOptions(formik.values["Category"])}
												label={t("SubCategory")}
												placeholder={t("placeholder", { ns: "common" })}
												error={formik.errors["SubCategory"]}
												//touched={formik.touched["SubCategory"]}
												isDisabled={readOnly}
												onChange={(firstArg) => {
													handleChangeEvent(
														"selectableTags",
														firstArg,
														"SubCategory",
														formik,
														formKey
													);
													handleChangeEvent(
														"selectableTags",
														[],
														"ChildEligibilityforWomeninDifficulty",
														formik,
														formKey
													);
													setTimeout(() => formik.setFieldTouched("SubCategory", true));
												}}
											/>
											{formik.values["SubCategory"]?.value === UNDER_45_AGE && (
												<Text color={"red"} pt={2}>
													{t("jobseekerErrorMsg")}
												</Text>
											)}
										</>
									)}
								</GridItem>
							}

							{/* fields to show if persona selected is Unemployed household with low income and Sub persona is between 25 - 44 years old  */}

							{formik.values["Category"] &&
								formik.values["SubCategory"]?.value === UNDER_45_AGE && (
									<>
										<GridItem colSpan={{ base: 2, md: 1 }}>
											<FormField
												type="radio"
												label={t("IsActiveStudent")}
												name="IsActiveStudent"
												touched={formik.touched["IsActiveStudent"]}
												value={formik.values["IsActiveStudent"]}
												options={lookups.Boolean}
												isReadOnly={readOnly}
												onChange={(firstArg) => {
													handleChangeEvent("radio", firstArg, "IsActiveStudent", formik, formKey);
												}}
											/>
										</GridItem>
										<GridItem colSpan={{ base: 2, md: 1 }}>
											<FormField
												type="radio"
												label={t("ReceivedLocalSupport")}
												name="ReceivedLocalSupport"
												value={formik.values["ReceivedLocalSupport"]}
												touched={formik.touched["ReceivedLocalSupport"]}
												options={lookups.Boolean}
												isReadOnly={readOnly}
												onChange={(firstArg) => {
													handleChangeEvent(
														"radio",
														firstArg,
														"ReceivedLocalSupport",
														formik,
														formKey
													);
												}}
											/>
											<Text color={"#1b1d21b8"} mt={1.5}>
												{t("localSupText")}
											</Text>
										</GridItem>
										<GridItem colSpan={{ base: 2, md: 1 }}>
											<FormField
												type="selectableTags"
												value={formik.values["MilitaryServiceStatus"]}
												isRequired={true}
												name="MilitaryServiceStatus"
												options={
													router.locale === "en" ? MilitaryServiceStatus : MilitaryServiceStatusAr
												}
												label={t("MilitaryServiceStatus")}
												placeholder={t("placeholder", { ns: "common" })}
												error={formik.errors["MilitaryServiceStatus"]}
												touched={formik.touched["MilitaryServiceStatus"]}
												isDisabled={readOnly}
												onChange={(firstArg) => {
													handleChangeEvent(
														"selectableTags",
														firstArg,
														"MilitaryServiceStatus",
														formik,
														formKey
													);
												}}
											/>
										</GridItem>
										<GridItem colSpan={{ base: 2, md: 1 }}>
											<FormField
												type="selectableTags"
												value={formik.values["Terminated"]}
												isRequired={true}
												name="Terminated"
												options={router.locale === "en" ? Terminated : TerminatedAr}
												label={t("Terminated")}
												placeholder={t("placeholder", { ns: "common" })}
												error={formik.errors["Terminated"]}
												touched={formik.touched["Terminated"]}
												isDisabled={readOnly}
												onChange={(firstArg) => {
													handleChangeEvent(
														"selectableTags",
														firstArg,
														"Terminated",
														formik,
														formKey
													);
												}}
											/>
										</GridItem>
										{/* DateOfTermination field - only show when Terminated value is "1" (I have been terminated from Public/Semi-Government) */}
										{formik.values["Terminated"]?.value === "1" && (
											<GridItem colSpan={{ base: 2, md: 1 }}>
												<FormField
													type="datetime"
													isRequired={true}
													value={formik.values["DateOfTermination"]}
													name="DateOfTermination"
													label={t("DateOfTermination")}
													placeholder={t("placeholder", { ns: "common" })}
													error={formik.errors["DateOfTermination"]}
													touched={formik.touched["DateOfTermination"]}
													maxDate={new Date()}
													isDisabled={readOnly}
													onChange={(firstArg) => {
														handleChangeEvent("datetime", firstArg, "DateOfTermination", formik, formKey);
													}}
												/>
											</GridItem>
										)}
										{/* <GridItem colSpan={{ base: 2, md: 1 }}>
										<FormField
											type="selectableTags"
											value={formik.values["Educations"]}
											tooltip={t("beneficiaryEducationTooltip")}
											isRequired={requiredList["Educations"] || false}
											options={lookups.Educations}
											name="Educations"
											label={t("Educations")}
											placeholder={t("placeholder", { ns: "common" })}
											error={formik.errors["Educations"]}
											isDisabled={readOnly}
											onChange={(firstArg) => {
												handleChangeEvent(
													"selectableTags",
													firstArg,
													"Educations",
													formik,
													formKey
												);
											}}
										/>
									</GridItem> */}

									</>
								)}

							{/* <GridItem colSpan={{ base: 2, md: 1 }}>
								<FormField
									type="selectableTags"
									value={formik.values["MaritalStatus"]}
									isRequired={requiredList["MaritalStatus"] || false}
									name="MaritalStatus"
									options={lookups.MaritalStatus}
									label={t("MaritalStatus")}
									placeholder={t("placeholder", { ns: "common" })}
									error={formik.errors["MaritalStatus"]}
									touched={formik.touched["MaritalStatus"]}
									isDisabled={!isMartialEmpty || checkMartial}
									onChange={(firstArg) => {
										handleChangeEvent("selectableTags", firstArg, "MaritalStatus", formik, formKey);
									}}
								/>
							</GridItem> */}
							{/* {false && (
								<GridItem colSpan={{ base: 2, md: 1 }}>
									<FormField
										type="selectableTags"
										isMulti
										value={formik.values["accomadations"]}
										options={lookups.accomadations}
										isRequired={requiredList["accomadations"] || false}
										name="accomadations"
										label={t("accomadations")}
										isDisabled={readOnly}
										placeholder={t("placeholder", { ns: "common" })}
										error={formik.errors["accomadations"]}
										touched={formik.touched["accomadations"]}
										onChange={(firstArg) => {
											handleChangeEvent(
												"selectableTags",
												firstArg,
												"accomadations",
												formik,
												formKey
											);
										}}
									/>
								</GridItem>
							)} */}
							{formik.values["Category"] &&
								(formik.values["SubCategory"]?.value === DIVORCED ||
									formik.values["SubCategory"]?.value === SPOUSE_INCAPACITATED_FOREIGNER ||
									formik.values["SubCategory"]?.value === ABANDONED) && (
									<GridItem colSpan={{ base: 2, md: 1 }}>
										<FormField
											type="selectableTags"
											isMulti
											value={formik.values["ChildEligibilityforWomeninDifficulty"]}
											options={
												router.locale === "en"
													? ChildEligibilityforWomeninDifficulty
													: ChildEligibilityforWomeninDifficultyAr
											}
											isRequired={true}
											name="ChildEligibilityforWomeninDifficulty"
											label={
												formik.values["SubCategory"]?.value === DIVORCED
													? t("ChildEligibilityforWomeninDifficulty")
													: t("select")
											}
											isDisabled={readOnly}
											placeholder={t("placeholder", { ns: "common" })}
											error={formik.errors["ChildEligibilityforWomeninDifficulty"]}
											touched={formik.touched["ChildEligibilityforWomeninDifficulty"]}
											onChange={(firstArg) => {
												let lastItem = firstArg[firstArg.length - 1];
												if (firstArg.length > 1) {
													if (lastItem && lastItem.value === "662410003") {
														firstArg = firstArg.filter((item) => item.value === "662410003");
														setDropDownError(true);
													} else {
														firstArg = firstArg.filter((item) => item.value !== "662410003");
													}
												}

												handleChangeEvent(
													"selectableTags",
													firstArg,
													"ChildEligibilityforWomeninDifficulty",
													formik,
													formKey
												);
											}}
										/>
										{dropDownError && (
											<Text color={"red"} pt={2}>
												{t("notApplicableErrorMsg")}
											</Text>
										)}
									</GridItem>
								)}

							{formik.values["Category"] &&
								(formik.values["SubCategory"]?.value === DIVORCED ||
									formik.values["SubCategory"]?.value === SPOUSE_INCAPACITATED_FOREIGNER ||
									formik.values["SubCategory"]?.value === ABANDONED) &&
								formik.values["ChildEligibilityforWomeninDifficulty"].length > 0 &&
								!formik.values["ChildEligibilityforWomeninDifficulty"]?.find(
									(item) => item.value === "662410003"
								) && (
									<GridItem colSpan={{ base: 2, md: 1 }}>
										<FormField
											type="text"
											isDisabled={readOnly}
											name="NumberOfChildren"
											placeholder={t("placeholder", { ns: "common" })}
											label={t("NumberOfChildren")}
											value={formik.values["NumberOfChildren"]}
											error={formik.errors["NumberOfChildren"]}
											onChange={(firstArg) => {
												handleChangeEvent("text", firstArg, "NumberOfChildren", formik, formKey);
											}}
										/>
									</GridItem>
								)}

							{formik.values["Category"] &&
								(formik.values["SubCategory"]?.value === DIVORCED ||
									formik.values["SubCategory"]?.value === SPOUSE_INCAPACITATED_FOREIGNER ||
									formik.values["SubCategory"]?.value === ABANDONED) &&
								formik.values["ChildEligibilityforWomeninDifficulty"].length > 0 &&
								!formik.values["ChildEligibilityforWomeninDifficulty"]?.find(
									(item) => item.value === "662410003"
								) &&
								formik.values["ChildEligibilityforWomeninDifficulty"]?.find(
									(item) => item.value === "662410002"
								) && (
									<GridItem colSpan={{ base: 2, md: 1 }}>
										<FormField
											type="text"
											isDisabled={readOnly}
											name="NumberOfChildrenLessThan25"
											placeholder={t("placeholder", { ns: "common" })}
											label={t("NumberOfChildrenLessThan25")}
											value={formik.values["NumberOfChildrenLessThan25"]}
											error={formik.errors["NumberOfChildrenLessThan25"]}
											onChange={(firstArg) => {
												handleChangeEvent(
													"text",
													firstArg,
													"NumberOfChildrenLessThan25",
													formik,
													formKey
												);
											}}
										/>
									</GridItem>
								)}

							{/* This field should open if persona is Women in Difficult Situation and Sub persona Divorced*/}
							{/* {formik.values["Category"] && formik.values["SubCategory"]?.value === DIVORCED && (
								<GridItem colSpan={{ base: 2, md: 1 }}>
									<FormField
										type="radio"
										label={t("HaveChildrenCustody")}
										name="HaveChildrenCustody"
										value={formik.values["HaveChildrenCustody"]}
										touched={formik.touched["HaveChildrenCustody"]}
										error={formik.errors["HaveChildrenCustody"]}
										options={lookups.Boolean}
										isReadOnly={readOnly}
										onChange={(firstArg) => {
											handleChangeEvent("radio", firstArg, "HaveChildrenCustody", formik, formKey);
										}}
									/>
								</GridItem>
							)} */}

							{/* fields to show if the persona is  Children in Difficult Situation */}

							{formik.values["Category"]?.value === CHILD_IN_DIFFICULT_SITUATION &&
								beneficiaryAge >= 21 &&
								(formik.values["SubCategory"]?.value === CHILD_OF_PRISONER ||
									formik.values["SubCategory"]?.value === ORPHANS_ONLY) && (
									<>
										<GridItem colSpan={{ base: 2, md: 1 }}>
											<FormField
												type="radio"
												label={t("PursuingHigherEducation")}
												name="PursuingHigherEducation"
												value={formik.values["PursuingHigherEducation"]}
												touched={formik.touched["PursuingHigherEducation"]}
												options={lookups.Boolean}
												isReadOnly={readOnly}
												onChange={(firstArg) => {
													handleChangeEvent(
														"radio",
														firstArg,
														"PursuingHigherEducation",
														formik,
														formKey
													);
												}}
											/>
										</GridItem>
										<GridItem colSpan={{ base: 2, md: 1 }}>
											<FormField
												type="radio"
												label={t("PursuingMilitaryService")}
												name="PursuingMilitaryService"
												value={formik.values["PursuingMilitaryService"]}
												touched={formik.touched["PursuingMilitaryService"]}
												options={lookups.Boolean}
												isReadOnly={readOnly}
												onChange={(firstArg) => {
													handleChangeEvent(
														"radio",
														firstArg,
														"PursuingMilitaryService",
														formik,
														formKey
													);
												}}
											/>
										</GridItem>
									</>
								)}

							{formik.values["Category"] &&
								formik.values["SubCategory"]?.value === BORN_UNKNOWN_PARENTS && (
									<GridItem colSpan={{ base: 2, md: 1 }}>
										<GridItem colSpan={{ base: 2, md: 1 }}>
											<FormField
												type="text"
												value={formik.values["GuardianEmiratesID"]}
												borderColor="brand.gray.250"
												isRequired={true}
												isDisabled={readOnly}
												customFormat={formatEmiratesID}
												name="GuardianEmiratesID"
												label={t("GuardianEmiratesID")}
												placeholder={t("placeholder", { ns: "common" })}
												touched={formik.touched["GuardianEmiratesID"]}
												error={formik.errors["GuardianEmiratesID"]}
												onChange={(firstArg) => {
													handleChangeEvent(
														"text",
														firstArg,
														"GuardianEmiratesID",
														formik,
														formKey
													);
												}}
											/>
										</GridItem>
									</GridItem>
								)}
						</Grid>
					</Form>
				);
			}}
		</Formik>
	);
}

export default RequestDetailsForm;
